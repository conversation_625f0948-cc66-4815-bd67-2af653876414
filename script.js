// Ocean Admin Console - Advanced Interactive Features

// Global variables for physics and interactions
let mouseX = 0;
let mouseY = 0;
let particles = [];
let fish = [];
let currentSystem = null;
let animationFrame = null;

document.addEventListener('DOMContentLoaded', function() {
    // Initialize the console
    initializeConsole();

    // Tab Navigation
    setupTabNavigation();

    // Real-time updates
    startRealTimeUpdates();

    // Interactive features
    setupInteractiveFeatures();

    // Modal functionality
    setupModal();

    // Advanced interactive systems
    initializePhysicsEngine();
    setupMouseInteractions();
    createParticleSystem();
    initializeFishAI();

    // Start animation loop
    startAnimationLoop();
});

function initializeConsole() {
    // Update current time
    updateCurrentTime();
    setInterval(updateCurrentTime, 1000);
    
    // Add welcome message
    console.log('🌊 Welcome to the Ocean Admin Console! 🐠');
    console.log('Just keep swimming, just keep managing!');
    
    // Initialize stats with animation
    animateStatsOnLoad();
}

function updateCurrentTime() {
    const now = new Date();
    const timeString = now.toLocaleTimeString('en-US', {
        hour12: true,
        hour: '2-digit',
        minute: '2-digit'
    });
    const timeElement = document.getElementById('currentTime');
    if (timeElement) {
        timeElement.textContent = `🕐 ${timeString}`;
    }
}

function setupTabNavigation() {
    const tabButtons = document.querySelectorAll('.tab-btn');
    const tabPanels = document.querySelectorAll('.tab-panel');
    
    tabButtons.forEach(button => {
        button.addEventListener('click', () => {
            const targetTab = button.getAttribute('data-tab');
            
            // Remove active class from all buttons and panels
            tabButtons.forEach(btn => btn.classList.remove('active'));
            tabPanels.forEach(panel => panel.classList.remove('active'));
            
            // Add active class to clicked button and corresponding panel
            button.classList.add('active');
            document.getElementById(targetTab).classList.add('active');
            
            // Add fun sound effect (visual feedback)
            button.style.transform = 'scale(0.95)';
            setTimeout(() => {
                button.style.transform = '';
            }, 150);
            
            // Log tab change with ocean theme
            const tabNames = {
                'dashboard': '🏠 Reef Dashboard',
                'users': '👥 School of Users',
                'analytics': '📊 Ocean Analytics',
                'settings': '⚙️ Coral Settings'
            };
            console.log(`🌊 Swimming to ${tabNames[targetTab]}...`);
        });
    });
}

function startRealTimeUpdates() {
    // Simulate real-time data updates
    setInterval(() => {
        updateActiveFish();
        updateShellsCollected();
        addRandomActivity();
    }, 5000);
    
    // Animate stats periodically
    setInterval(animateStats, 10000);
}

function updateActiveFish() {
    const activeFishElement = document.getElementById('activeFish');
    if (activeFishElement) {
        const currentCount = parseInt(activeFishElement.textContent.replace(',', ''));
        const change = Math.floor(Math.random() * 10) - 5; // Random change between -5 and +4
        const newCount = Math.max(1000, currentCount + change);
        
        activeFishElement.textContent = newCount.toLocaleString();
        
        // Add visual feedback for changes
        if (change > 0) {
            activeFishElement.style.color = '#27AE60';
            setTimeout(() => {
                activeFishElement.style.color = '';
            }, 1000);
        }
    }
}

function updateShellsCollected() {
    const shellsElement = document.getElementById('shellsCollected');
    if (shellsElement) {
        const currentCount = parseInt(shellsElement.textContent);
        const newCount = currentCount + Math.floor(Math.random() * 3);
        shellsElement.textContent = newCount;
        
        // Animate the update
        shellsElement.style.transform = 'scale(1.1)';
        setTimeout(() => {
            shellsElement.style.transform = '';
        }, 300);
    }
}

function addRandomActivity() {
    const activities = [
        "🐠 Nemo discovered a new anemone",
        "🐟 Dory remembered something important",
        "🦈 Bruce shared a vegetarian recipe",
        "🐡 Bloat inflated with excitement",
        "🐙 Pearl found a shiny object",
        "🦀 Sebastian played a new tune",
        "🐚 New shell added to collection",
        "🌊 Ocean current changed direction",
        "🪸 Coral reef expanded by 2%",
        "🐠 School of fish performed synchronized swimming"
    ];
    
    const activityFeed = document.querySelector('.activity-feed');
    if (activityFeed) {
        const activityItems = activityFeed.querySelectorAll('.activity-item');
        
        // Remove oldest activity if we have too many
        if (activityItems.length >= 5) {
            activityItems[activityItems.length - 1].remove();
        }
        
        // Create new activity
        const newActivity = document.createElement('div');
        newActivity.className = 'activity-item';
        newActivity.style.opacity = '0';
        newActivity.style.transform = 'translateY(-20px)';
        
        const randomActivity = activities[Math.floor(Math.random() * activities.length)];
        newActivity.innerHTML = `
            <span class="activity-time">Just now</span>
            <span class="activity-text">${randomActivity}</span>
        `;
        
        // Insert at the beginning
        const firstActivity = activityFeed.querySelector('.activity-item');
        if (firstActivity) {
            activityFeed.insertBefore(newActivity, firstActivity);
        } else {
            activityFeed.appendChild(newActivity);
        }
        
        // Animate in
        setTimeout(() => {
            newActivity.style.transition = 'all 0.5s ease';
            newActivity.style.opacity = '1';
            newActivity.style.transform = 'translateY(0)';
        }, 100);
    }
}

function animateStatsOnLoad() {
    const statCards = document.querySelectorAll('.stat-card');
    statCards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(30px)';
        
        setTimeout(() => {
            card.style.transition = 'all 0.6s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 200);
    });
}

function animateStats() {
    const statIcons = document.querySelectorAll('.stat-icon');
    statIcons.forEach(icon => {
        icon.style.animation = 'none';
        setTimeout(() => {
            icon.style.animation = 'bounce 2s infinite';
        }, 100);
    });
}

function setupInteractiveFeatures() {
    // Add hover effects to user cards
    const userCards = document.querySelectorAll('.user-card');
    userCards.forEach(card => {
        card.addEventListener('mouseenter', () => {
            const avatar = card.querySelector('.user-avatar');
            avatar.style.transform = 'scale(1.1) rotate(10deg)';
        });
        
        card.addEventListener('mouseleave', () => {
            const avatar = card.querySelector('.user-avatar');
            avatar.style.transform = '';
        });
    });
    
    // Add click effects to buttons
    const buttons = document.querySelectorAll('button');
    buttons.forEach(button => {
        button.addEventListener('click', (e) => {
            // Create ripple effect
            const ripple = document.createElement('span');
            ripple.style.position = 'absolute';
            ripple.style.borderRadius = '50%';
            ripple.style.background = 'rgba(255, 255, 255, 0.6)';
            ripple.style.transform = 'scale(0)';
            ripple.style.animation = 'ripple 0.6s linear';
            ripple.style.left = e.offsetX + 'px';
            ripple.style.top = e.offsetY + 'px';
            ripple.style.width = ripple.style.height = '20px';
            ripple.style.marginLeft = ripple.style.marginTop = '-10px';
            
            button.style.position = 'relative';
            button.style.overflow = 'hidden';
            button.appendChild(ripple);
            
            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
    });
    
    // Add range slider interactions
    const rangeSliders = document.querySelectorAll('input[type="range"]');
    rangeSliders.forEach(slider => {
        const updateValue = () => {
            const span = slider.parentElement.querySelector('span');
            if (span) {
                if (slider.min === '20' && slider.max === '30') {
                    span.textContent = slider.value + '°C';
                } else {
                    span.textContent = slider.value + '/10';
                }
            }
        };
        
        slider.addEventListener('input', updateValue);
        updateValue(); // Initialize
    });
}

function setupModal() {
    const fabBtn = document.getElementById('fabBtn');
    const modal = document.getElementById('helpModal');
    const closeBtn = modal.querySelector('.close');
    
    fabBtn.addEventListener('click', () => {
        modal.style.display = 'block';
        console.log('🐟 Dory says: "I can help you navigate the ocean!"');
    });
    
    closeBtn.addEventListener('click', () => {
        modal.style.display = 'none';
    });
    
    window.addEventListener('click', (e) => {
        if (e.target === modal) {
            modal.style.display = 'none';
        }
    });
}

function addFunFeatures() {
    // Add CSS for ripple animation
    const style = document.createElement('style');
    style.textContent = `
        @keyframes ripple {
            to {
                transform: scale(4);
                opacity: 0;
            }
        }
    `;
    document.head.appendChild(style);
    
    // Add random encouraging messages
    const encouragingMessages = [
        "🐠 Just keep swimming!",
        "🌊 You're doing great!",
        "🐟 Dory believes in you!",
        "🦈 Bruce says you're awesome!",
        "🐡 Keep up the good work!",
        "🪸 The reef is proud of you!"
    ];
    
    setInterval(() => {
        const randomMessage = encouragingMessages[Math.floor(Math.random() * encouragingMessages.length)];
        console.log(randomMessage);
    }, 30000); // Every 30 seconds
    
    // Add keyboard shortcuts
    document.addEventListener('keydown', (e) => {
        if (e.ctrlKey || e.metaKey) {
            switch(e.key) {
                case '1':
                    e.preventDefault();
                    document.querySelector('[data-tab="dashboard"]').click();
                    break;
                case '2':
                    e.preventDefault();
                    document.querySelector('[data-tab="users"]').click();
                    break;
                case '3':
                    e.preventDefault();
                    document.querySelector('[data-tab="analytics"]').click();
                    break;
                case '4':
                    e.preventDefault();
                    document.querySelector('[data-tab="settings"]').click();
                    break;
            }
        }
    });
    
    console.log('🌊 Keyboard shortcuts: Ctrl+1-4 to navigate tabs!');
}

// Advanced Physics and Interaction Systems

function initializePhysicsEngine() {
    // Create physics-enabled fish objects
    const fishElements = document.querySelectorAll('.fish');
    fishElements.forEach((fishEl, index) => {
        const fishObj = {
            element: fishEl,
            x: Math.random() * window.innerWidth,
            y: Math.random() * window.innerHeight * 0.8 + 100,
            vx: (Math.random() - 0.5) * 2,
            vy: (Math.random() - 0.5) * 1,
            targetX: 0,
            targetY: 0,
            mass: 1 + Math.random() * 2,
            avoidanceRadius: 100,
            followMouse: false,
            type: fishEl.classList[1] || 'default'
        };
        fish.push(fishObj);

        // Position fish initially
        fishEl.style.position = 'absolute';
        fishEl.style.left = fishObj.x + 'px';
        fishEl.style.top = fishObj.y + 'px';
        fishEl.style.transition = 'none';
        fishEl.style.cursor = 'pointer';

        // Add click interaction
        fishEl.addEventListener('click', () => toggleFishFollow(fishObj));
    });
}

function setupMouseInteractions() {
    // Track mouse movement
    document.addEventListener('mousemove', (e) => {
        mouseX = e.clientX;
        mouseY = e.clientY;

        // Create mouse trail particles
        if (Math.random() < 0.3) {
            createParticle(mouseX, mouseY, 'trail');
        }

        // Apply mouse influence to nearby fish
        fish.forEach(fishObj => {
            const distance = Math.sqrt(
                Math.pow(fishObj.x - mouseX, 2) +
                Math.pow(fishObj.y - mouseY, 2)
            );

            if (distance < 150 && !fishObj.followMouse) {
                // Fish avoidance behavior
                const angle = Math.atan2(fishObj.y - mouseY, fishObj.x - mouseX);
                fishObj.vx += Math.cos(angle) * 0.5;
                fishObj.vy += Math.sin(angle) * 0.5;
            }
        });
    });

    // Click to create ripple effect
    document.addEventListener('click', (e) => {
        if (!e.target.closest('.admin-console')) {
            createRippleEffect(e.clientX, e.clientY);
            createBurstParticles(e.clientX, e.clientY);
        }
    });
}

function toggleFishFollow(fishObj) {
    fishObj.followMouse = !fishObj.followMouse;

    if (fishObj.followMouse) {
        fishObj.element.style.filter = 'brightness(1.3) drop-shadow(0 0 10px rgba(255, 255, 255, 0.8))';
        console.log(`🐠 ${fishObj.type} is now following your cursor!`);
    } else {
        fishObj.element.style.filter = '';
        console.log(`🐠 ${fishObj.type} is swimming freely again.`);
    }
}

function createParticleSystem() {
    // Initialize particle system
    currentSystem = {
        bubbles: [],
        debris: [],
        lightRays: []
    };

    // Create continuous bubble generation
    setInterval(() => {
        if (particles.length < 50) {
            createParticle(
                Math.random() * window.innerWidth,
                window.innerHeight + 20,
                'bubble'
            );
        }
    }, 500);

    // Create floating debris occasionally
    setInterval(() => {
        if (Math.random() < 0.3) {
            createParticle(
                Math.random() * window.innerWidth,
                Math.random() * window.innerHeight,
                'debris'
            );
        }
    }, 3000);

    // Create light rays
    createLightRays();
}

function createParticle(x, y, type) {
    const particle = {
        x: x,
        y: y,
        vx: (Math.random() - 0.5) * 2,
        vy: type === 'bubble' ? -1 - Math.random() * 2 : (Math.random() - 0.5) * 0.5,
        life: 1.0,
        decay: type === 'trail' ? 0.05 : 0.01,
        size: type === 'bubble' ? 3 + Math.random() * 8 : 2 + Math.random() * 4,
        type: type,
        element: null
    };

    // Create DOM element for particle
    const element = document.createElement('div');
    element.className = `particle particle-${type}`;
    element.style.position = 'absolute';
    element.style.left = x + 'px';
    element.style.top = y + 'px';
    element.style.width = particle.size + 'px';
    element.style.height = particle.size + 'px';
    element.style.pointerEvents = 'none';
    element.style.zIndex = '5';

    // Style based on type
    switch(type) {
        case 'bubble':
            element.style.background = 'radial-gradient(circle, rgba(255,255,255,0.8) 0%, rgba(255,255,255,0.3) 70%, transparent 100%)';
            element.style.borderRadius = '50%';
            element.style.border = '1px solid rgba(255,255,255,0.4)';
            break;
        case 'trail':
            element.style.background = 'radial-gradient(circle, rgba(100,200,255,0.6) 0%, transparent 70%)';
            element.style.borderRadius = '50%';
            break;
        case 'debris':
            element.style.background = 'rgba(139, 69, 19, 0.6)';
            element.style.borderRadius = '2px';
            element.style.transform = 'rotate(' + Math.random() * 360 + 'deg)';
            break;
    }

    particle.element = element;
    document.body.appendChild(element);
    particles.push(particle);
}

function createRippleEffect(x, y) {
    const ripple = document.createElement('div');
    ripple.style.position = 'absolute';
    ripple.style.left = (x - 25) + 'px';
    ripple.style.top = (y - 25) + 'px';
    ripple.style.width = '50px';
    ripple.style.height = '50px';
    ripple.style.border = '2px solid rgba(100, 200, 255, 0.8)';
    ripple.style.borderRadius = '50%';
    ripple.style.pointerEvents = 'none';
    ripple.style.zIndex = '10';
    ripple.style.animation = 'rippleExpand 1s ease-out forwards';

    document.body.appendChild(ripple);

    setTimeout(() => {
        ripple.remove();
    }, 1000);
}

function createBurstParticles(x, y) {
    for (let i = 0; i < 8; i++) {
        const angle = (i / 8) * Math.PI * 2;
        const speed = 2 + Math.random() * 3;

        const particle = {
            x: x,
            y: y,
            vx: Math.cos(angle) * speed,
            vy: Math.sin(angle) * speed,
            life: 1.0,
            decay: 0.02,
            size: 3 + Math.random() * 4,
            type: 'burst',
            element: null
        };

        const element = document.createElement('div');
        element.style.position = 'absolute';
        element.style.left = x + 'px';
        element.style.top = y + 'px';
        element.style.width = particle.size + 'px';
        element.style.height = particle.size + 'px';
        element.style.background = 'radial-gradient(circle, rgba(100,200,255,0.9) 0%, transparent 70%)';
        element.style.borderRadius = '50%';
        element.style.pointerEvents = 'none';
        element.style.zIndex = '8';

        particle.element = element;
        document.body.appendChild(element);
        particles.push(particle);
    }
}

function createLightRays() {
    for (let i = 0; i < 5; i++) {
        const ray = document.createElement('div');
        ray.className = 'light-ray';
        ray.style.position = 'absolute';
        ray.style.top = '0';
        ray.style.left = (i * 20 + Math.random() * window.innerWidth * 0.8) + 'px';
        ray.style.width = '2px';
        ray.style.height = '100vh';
        ray.style.background = 'linear-gradient(to bottom, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 50%, transparent 100%)';
        ray.style.pointerEvents = 'none';
        ray.style.zIndex = '1';
        ray.style.animation = `lightRayMove ${10 + Math.random() * 10}s ease-in-out infinite`;
        ray.style.animationDelay = Math.random() * 5 + 's';

        document.body.appendChild(ray);
    }
}

function initializeFishAI() {
    // Enhanced fish behavior with flocking and current simulation
    fish.forEach(fishObj => {
        fishObj.schoolmates = fish.filter(f => f !== fishObj);
    });
}

function startAnimationLoop() {
    function animate() {
        updateFishPhysics();
        updateParticles();
        simulateWaterCurrent();

        animationFrame = requestAnimationFrame(animate);
    }
    animate();
}

function updateFishPhysics() {
    fish.forEach(fishObj => {
        // Apply flocking behavior
        if (!fishObj.followMouse) {
            applyFlockingBehavior(fishObj);
        } else {
            // Follow mouse with smooth movement
            const dx = mouseX - fishObj.x;
            const dy = mouseY - fishObj.y;
            fishObj.vx += dx * 0.001;
            fishObj.vy += dy * 0.001;
        }

        // Apply water current simulation
        const currentStrength = 0.1;
        fishObj.vx += Math.sin(Date.now() * 0.001 + fishObj.x * 0.01) * currentStrength;
        fishObj.vy += Math.cos(Date.now() * 0.0008 + fishObj.y * 0.01) * currentStrength * 0.5;

        // Apply velocity damping
        fishObj.vx *= 0.98;
        fishObj.vy *= 0.98;

        // Limit velocity
        const maxSpeed = fishObj.followMouse ? 3 : 1.5;
        const speed = Math.sqrt(fishObj.vx * fishObj.vx + fishObj.vy * fishObj.vy);
        if (speed > maxSpeed) {
            fishObj.vx = (fishObj.vx / speed) * maxSpeed;
            fishObj.vy = (fishObj.vy / speed) * maxSpeed;
        }

        // Update position
        fishObj.x += fishObj.vx;
        fishObj.y += fishObj.vy;

        // Boundary collision with bounce
        if (fishObj.x < 0) {
            fishObj.x = 0;
            fishObj.vx = Math.abs(fishObj.vx);
        }
        if (fishObj.x > window.innerWidth - 50) {
            fishObj.x = window.innerWidth - 50;
            fishObj.vx = -Math.abs(fishObj.vx);
        }
        if (fishObj.y < 100) {
            fishObj.y = 100;
            fishObj.vy = Math.abs(fishObj.vy);
        }
        if (fishObj.y > window.innerHeight - 100) {
            fishObj.y = window.innerHeight - 100;
            fishObj.vy = -Math.abs(fishObj.vy);
        }

        // Update DOM element
        fishObj.element.style.left = fishObj.x + 'px';
        fishObj.element.style.top = fishObj.y + 'px';

        // Flip fish based on direction
        if (fishObj.vx > 0) {
            fishObj.element.style.transform = 'scaleX(1)';
        } else if (fishObj.vx < 0) {
            fishObj.element.style.transform = 'scaleX(-1)';
        }
    });
}

function applyFlockingBehavior(fishObj) {
    let separationX = 0, separationY = 0;
    let alignmentX = 0, alignmentY = 0;
    let cohesionX = 0, cohesionY = 0;
    let neighborCount = 0;

    fishObj.schoolmates.forEach(other => {
        const dx = other.x - fishObj.x;
        const dy = other.y - fishObj.y;
        const distance = Math.sqrt(dx * dx + dy * dy);

        if (distance < 80 && distance > 0) {
            // Separation - avoid crowding
            separationX -= dx / distance;
            separationY -= dy / distance;

            // Alignment - steer towards average heading
            alignmentX += other.vx;
            alignmentY += other.vy;

            // Cohesion - steer towards average position
            cohesionX += other.x;
            cohesionY += other.y;

            neighborCount++;
        }
    });

    if (neighborCount > 0) {
        // Apply separation
        fishObj.vx += separationX * 0.05;
        fishObj.vy += separationY * 0.05;

        // Apply alignment
        alignmentX /= neighborCount;
        alignmentY /= neighborCount;
        fishObj.vx += (alignmentX - fishObj.vx) * 0.02;
        fishObj.vy += (alignmentY - fishObj.vy) * 0.02;

        // Apply cohesion
        cohesionX /= neighborCount;
        cohesionY /= neighborCount;
        fishObj.vx += (cohesionX - fishObj.x) * 0.001;
        fishObj.vy += (cohesionY - fishObj.y) * 0.001;
    }
}

function updateParticles() {
    for (let i = particles.length - 1; i >= 0; i--) {
        const particle = particles[i];

        // Update position
        particle.x += particle.vx;
        particle.y += particle.vy;

        // Apply physics based on type
        if (particle.type === 'bubble') {
            // Buoyancy effect
            particle.vy -= 0.02;
            // Slight horizontal drift
            particle.vx += (Math.random() - 0.5) * 0.1;
        } else if (particle.type === 'debris') {
            // Gravity and water resistance
            particle.vy += 0.01;
            particle.vx *= 0.99;
            particle.vy *= 0.99;
        }

        // Update life
        particle.life -= particle.decay;

        // Update DOM element
        if (particle.element) {
            particle.element.style.left = particle.x + 'px';
            particle.element.style.top = particle.y + 'px';
            particle.element.style.opacity = particle.life;

            // Scale effect for some particles
            if (particle.type === 'burst') {
                const scale = particle.life;
                particle.element.style.transform = `scale(${scale})`;
            }
        }

        // Remove dead particles
        if (particle.life <= 0 || particle.y < -50 || particle.y > window.innerHeight + 50) {
            if (particle.element) {
                particle.element.remove();
            }
            particles.splice(i, 1);
        }
    }
}

function simulateWaterCurrent() {
    // Create visible current effects occasionally
    if (Math.random() < 0.02) {
        const currentParticle = {
            x: Math.random() * window.innerWidth,
            y: Math.random() * window.innerHeight,
            vx: 1 + Math.random() * 2,
            vy: (Math.random() - 0.5) * 0.5,
            life: 1.0,
            decay: 0.005,
            size: 1,
            type: 'current',
            element: null
        };

        const element = document.createElement('div');
        element.style.position = 'absolute';
        element.style.left = currentParticle.x + 'px';
        element.style.top = currentParticle.y + 'px';
        element.style.width = '20px';
        element.style.height = '1px';
        element.style.background = 'rgba(100, 200, 255, 0.3)';
        element.style.pointerEvents = 'none';
        element.style.zIndex = '2';

        currentParticle.element = element;
        document.body.appendChild(element);
        particles.push(currentParticle);
    }
}

// Add CSS animations for new effects
function addAdvancedStyles() {
    const style = document.createElement('style');
    style.textContent = `
        @keyframes rippleExpand {
            0% {
                transform: scale(1);
                opacity: 1;
            }
            100% {
                transform: scale(8);
                opacity: 0;
            }
        }

        @keyframes lightRayMove {
            0%, 100% {
                transform: translateX(-10px);
                opacity: 0.1;
            }
            50% {
                transform: translateX(10px);
                opacity: 0.3;
            }
        }

        .particle {
            transition: none;
        }

        .fish {
            transition: transform 0.1s ease-out;
            z-index: 6;
        }

        .fish:hover {
            filter: brightness(1.2) drop-shadow(0 0 8px rgba(255, 255, 255, 0.6)) !important;
            transform: scale(1.1) !important;
        }
    `;
    document.head.appendChild(style);
}

// Initialize advanced styles when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', addAdvancedStyles);
} else {
    addAdvancedStyles();
}
