/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Nunito', sans-serif;
    background: linear-gradient(180deg, #87CEEB 0%, #4682B4 50%, #191970 100%);
    min-height: 100vh;
    overflow-x: hidden;
    position: relative;
}

/* Ocean Background */
.ocean-background {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    overflow: hidden;
}

/* Bubbles Animation */
.bubbles {
    position: absolute;
    width: 100%;
    height: 100%;
}

.bubble {
    position: absolute;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    animation: bubble-rise 8s infinite linear;
}

.bubble:nth-child(1) { width: 10px; height: 10px; left: 10%; animation-delay: 0s; }
.bubble:nth-child(2) { width: 15px; height: 15px; left: 20%; animation-delay: 1s; }
.bubble:nth-child(3) { width: 8px; height: 8px; left: 35%; animation-delay: 2s; }
.bubble:nth-child(4) { width: 12px; height: 12px; left: 50%; animation-delay: 3s; }
.bubble:nth-child(5) { width: 18px; height: 18px; left: 65%; animation-delay: 4s; }
.bubble:nth-child(6) { width: 6px; height: 6px; left: 80%; animation-delay: 5s; }
.bubble:nth-child(7) { width: 14px; height: 14px; left: 90%; animation-delay: 6s; }
.bubble:nth-child(8) { width: 9px; height: 9px; left: 75%; animation-delay: 7s; }

@keyframes bubble-rise {
    0% {
        bottom: -100px;
        opacity: 0;
    }
    10% {
        opacity: 1;
    }
    90% {
        opacity: 1;
    }
    100% {
        bottom: 100vh;
        opacity: 0;
    }
}

/* Swimming Fish - Enhanced with Physics */
.fish-container {
    position: absolute;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.fish {
    position: absolute;
    font-size: 2rem;
    cursor: pointer;
    pointer-events: all;
    transition: filter 0.3s ease, transform 0.1s ease-out;
    z-index: 6;
    user-select: none;
    will-change: transform, left, top;
}

.fish:hover {
    filter: brightness(1.2) drop-shadow(0 0 8px rgba(255, 255, 255, 0.6));
    transform: scale(1.1);
}

.fish:active {
    transform: scale(0.95);
}

/* Remove old animation - now controlled by JavaScript */
.fish.nemo,
.fish.dory,
.fish.bruce,
.fish.yellow-fish {
    animation: none;
}

/* Enhanced particle effects */
.particle {
    pointer-events: none;
    will-change: transform, opacity, left, top;
}

.particle-bubble {
    animation: bubbleFloat 0.5s ease-out;
}

.particle-trail {
    animation: trailFade 1s ease-out forwards;
}

.particle-debris {
    animation: debrisFloat 2s ease-out;
}

@keyframes bubbleFloat {
    0% {
        transform: scale(0);
        opacity: 0;
    }
    50% {
        opacity: 1;
    }
    100% {
        transform: scale(1);
        opacity: 0.8;
    }
}

@keyframes trailFade {
    0% {
        transform: scale(1);
        opacity: 0.8;
    }
    100% {
        transform: scale(0.3);
        opacity: 0;
    }
}

@keyframes debrisFloat {
    0% {
        opacity: 0;
        transform: rotate(0deg);
    }
    50% {
        opacity: 0.6;
    }
    100% {
        opacity: 0;
        transform: rotate(180deg);
    }
}

/* Seaweed */
.seaweed {
    position: absolute;
    bottom: 0;
    width: 30px;
    background: linear-gradient(to top, #228B22, #32CD32);
    border-radius: 15px 15px 0 0;
    animation: sway 4s ease-in-out infinite;
}

.seaweed-1 {
    left: 5%;
    height: 150px;
    animation-delay: 0s;
}

.seaweed-2 {
    left: 15%;
    height: 120px;
    animation-delay: 1s;
}

.seaweed-3 {
    right: 10%;
    height: 180px;
    animation-delay: 2s;
}

@keyframes sway {
    0%, 100% { transform: rotate(-5deg); }
    50% { transform: rotate(5deg); }
}

/* Water Current Effects */
.current-line {
    position: absolute;
    background: linear-gradient(90deg, transparent 0%, rgba(100, 200, 255, 0.3) 50%, transparent 100%);
    height: 1px;
    pointer-events: none;
    z-index: 2;
}

/* Light Rays */
.light-ray {
    position: absolute;
    pointer-events: none;
    z-index: 1;
    opacity: 0.1;
    animation: lightRayMove 15s ease-in-out infinite;
}

@keyframes lightRayMove {
    0%, 100% {
        transform: translateX(-10px) rotate(2deg);
        opacity: 0.05;
    }
    50% {
        transform: translateX(10px) rotate(-2deg);
        opacity: 0.15;
    }
}

/* Ripple Effects */
@keyframes rippleExpand {
    0% {
        transform: scale(1);
        opacity: 0.8;
        border-width: 3px;
    }
    100% {
        transform: scale(12);
        opacity: 0;
        border-width: 0px;
    }
}

/* Enhanced Bubble Effects */
.bubbles .bubble {
    box-shadow: inset 0 0 10px rgba(255, 255, 255, 0.3);
    animation: bubble-rise-enhanced 8s infinite linear;
}

@keyframes bubble-rise-enhanced {
    0% {
        bottom: -100px;
        opacity: 0;
        transform: scale(0.5) translateX(0px);
    }
    10% {
        opacity: 1;
        transform: scale(1) translateX(0px);
    }
    50% {
        transform: scale(1.1) translateX(20px);
    }
    90% {
        opacity: 1;
        transform: scale(0.9) translateX(-10px);
    }
    100% {
        bottom: 100vh;
        opacity: 0;
        transform: scale(0.3) translateX(30px);
    }
}

/* Interactive Elements Enhancement */
.admin-console {
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Mouse interaction feedback */
body {
    cursor: crosshair;
}

.admin-console * {
    cursor: default;
}

.fish {
    cursor: grab;
}

.fish:active {
    cursor: grabbing;
}

/* Performance optimizations */
.fish,
.particle,
.bubble,
.light-ray {
    transform-style: preserve-3d;
    backface-visibility: hidden;
}

/* Admin Console */
.admin-console {
    position: relative;
    z-index: 10;
    max-width: 1200px;
    margin: 2rem auto;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(10px);
    overflow: hidden;
    animation: float 6s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

/* Header */
.console-header {
    background: linear-gradient(135deg, #FF6B35, #F7931E);
    color: white;
    padding: 2rem;
    text-align: center;
    position: relative;
}

.console-header h1 {
    font-family: 'Fredoka One', cursive;
    font-size: 2.5rem;
    margin-bottom: 0.5rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.subtitle {
    font-size: 1.2rem;
    opacity: 0.9;
    margin-bottom: 1rem;
}

.current-time {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: rgba(255, 255, 255, 0.2);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: 600;
}

/* Navigation */
.tab-navigation {
    display: flex;
    background: #2C3E50;
    overflow-x: auto;
}

.tab-btn {
    flex: 1;
    padding: 1rem 1.5rem;
    background: none;
    border: none;
    color: #BDC3C7;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
}

.tab-btn:hover {
    background: rgba(52, 152, 219, 0.2);
    color: #3498DB;
}

.tab-btn.active {
    background: #3498DB;
    color: white;
    transform: translateY(-2px);
}

/* Tab Content */
.tab-content {
    padding: 2rem;
}

.tab-panel {
    display: none;
    animation: fadeIn 0.5s ease-in-out;
}

.tab-panel.active {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1.5rem;
    border-radius: 15px;
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: transform 0.3s ease;
    cursor: pointer;
}

.stat-card:hover {
    transform: translateY(-5px) scale(1.02);
}

.stat-icon {
    font-size: 2.5rem;
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-10px); }
    60% { transform: translateY(-5px); }
}

.stat-info h3 {
    font-size: 1rem;
    opacity: 0.9;
    margin-bottom: 0.5rem;
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    margin: 0;
}

/* Activity Feed */
.activity-feed {
    background: #f8f9fa;
    border-radius: 15px;
    padding: 1.5rem;
}

.activity-feed h3 {
    color: #2C3E50;
    margin-bottom: 1rem;
    font-family: 'Fredoka One', cursive;
}

.activity-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid #e9ecef;
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-time {
    color: #6c757d;
    font-size: 0.9rem;
    min-width: 120px;
}

.activity-text {
    flex: 1;
    margin-left: 1rem;
}

/* Users Section */
.users-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.users-header h3 {
    color: #2C3E50;
    font-family: 'Fredoka One', cursive;
}

.add-user-btn {
    background: linear-gradient(135deg, #11998e, #38ef7d);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 25px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.add-user-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(17, 153, 142, 0.4);
}

.users-grid {
    display: grid;
    gap: 1rem;
}

.user-card {
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 15px;
    padding: 1rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: all 0.3s ease;
}

.user-card:hover {
    border-color: #3498DB;
    transform: translateX(5px);
}

.user-avatar {
    font-size: 2rem;
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.user-info {
    flex: 1;
}

.user-info h4 {
    color: #2C3E50;
    margin-bottom: 0.25rem;
}

.user-info p {
    color: #6c757d;
    font-size: 0.9rem;
}

.user-actions {
    display: flex;
    gap: 0.5rem;
}

.btn-edit, .btn-delete {
    background: none;
    border: none;
    font-size: 1.2rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 8px;
    transition: background 0.3s ease;
}

.btn-edit:hover {
    background: rgba(52, 152, 219, 0.1);
}

.btn-delete:hover {
    background: rgba(231, 76, 60, 0.1);
}

/* Analytics Section */
.chart-container {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.chart-placeholder {
    text-align: center;
    color: #2C3E50;
}

.chart-placeholder p {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.mock-chart {
    height: 200px;
    background: linear-gradient(45deg, #667eea, #764ba2, #f093fb, #f5576c);
    background-size: 400% 400%;
    border-radius: 10px;
    animation: gradient-wave 4s ease infinite;
    position: relative;
    overflow: hidden;
}

.mock-chart::before {
    content: "📊 Fish Population: Trending Up! 🐠";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-weight: 600;
    font-size: 1.1rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

@keyframes gradient-wave {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

/* Settings Section */
.settings-form {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.setting-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 10px;
}

.setting-item label {
    min-width: 150px;
    font-weight: 600;
    color: #2C3E50;
}

.setting-item input[type="range"] {
    flex: 1;
    height: 8px;
    border-radius: 5px;
    background: #ddd;
    outline: none;
    -webkit-appearance: none;
}

.setting-item input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #3498DB;
    cursor: pointer;
}

.setting-item select {
    flex: 1;
    padding: 0.5rem;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    background: white;
    font-size: 1rem;
}

.setting-item span {
    min-width: 60px;
    text-align: center;
    font-weight: 600;
    color: #3498DB;
}

/* Floating Action Button */
.fab {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    background: linear-gradient(135deg, #FF6B35, #F7931E);
    color: white;
    border: none;
    border-radius: 50px;
    padding: 1rem 1.5rem;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    box-shadow: 0 10px 25px rgba(255, 107, 53, 0.4);
    transition: all 0.3s ease;
    z-index: 1000;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    animation: pulse 2s infinite;
}

.fab:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 35px rgba(255, 107, 53, 0.6);
}

.fab-icon {
    font-size: 1.2rem;
}

@keyframes pulse {
    0% { box-shadow: 0 10px 25px rgba(255, 107, 53, 0.4); }
    50% { box-shadow: 0 10px 25px rgba(255, 107, 53, 0.8); }
    100% { box-shadow: 0 10px 25px rgba(255, 107, 53, 0.4); }
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
}

.modal-content {
    background: white;
    margin: 10% auto;
    padding: 2rem;
    border-radius: 20px;
    width: 90%;
    max-width: 500px;
    position: relative;
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.close {
    position: absolute;
    top: 1rem;
    right: 1.5rem;
    font-size: 2rem;
    font-weight: bold;
    cursor: pointer;
    color: #aaa;
    transition: color 0.3s ease;
}

.close:hover {
    color: #000;
}

.modal-content h2 {
    color: #2C3E50;
    margin-bottom: 1rem;
    font-family: 'Fredoka One', cursive;
}

.modal-content ul {
    list-style: none;
    padding: 0;
}

.modal-content li {
    padding: 0.5rem 0;
    border-bottom: 1px solid #e9ecef;
}

.modal-content li:last-child {
    border-bottom: none;
}

/* Responsive Design */
@media (max-width: 768px) {
    .admin-console {
        margin: 1rem;
        border-radius: 15px;
    }

    .console-header h1 {
        font-size: 2rem;
    }

    .tab-btn {
        padding: 0.75rem 1rem;
        font-size: 0.9rem;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .fab {
        bottom: 1rem;
        right: 1rem;
        padding: 0.75rem 1rem;
    }

    .fab-text {
        display: none;
    }

    .activity-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .activity-time {
        min-width: auto;
    }

    .activity-text {
        margin-left: 0;
    }
}
