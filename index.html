<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🐠 Ocean Admin Console - Finding Nemo Style</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Fredoka+One:wght@400&family=Nunito:wght@300;400;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Underwater Background -->
    <div class="ocean-background">
        <div class="bubbles">
            <div class="bubble"></div>
            <div class="bubble"></div>
            <div class="bubble"></div>
            <div class="bubble"></div>
            <div class="bubble"></div>
            <div class="bubble"></div>
            <div class="bubble"></div>
            <div class="bubble"></div>
        </div>
        
        <!-- Swimming Fish -->
        <div class="fish-container">
            <div class="fish nemo">🐠</div>
            <div class="fish dory">🐟</div>
            <div class="fish bruce">🦈</div>
            <div class="fish yellow-fish">🐡</div>
        </div>
        
        <!-- Seaweed -->
        <div class="seaweed seaweed-1"></div>
        <div class="seaweed seaweed-2"></div>
        <div class="seaweed seaweed-3"></div>
    </div>

    <!-- Main Console -->
    <div class="admin-console">
        <!-- Header -->
        <header class="console-header">
            <h1>🌊 Ocean Admin Console 🌊</h1>
            <p class="subtitle">Just keep swimming, just keep managing! 🐠</p>
            <div class="current-time" id="currentTime"></div>
        </header>

        <!-- Navigation Tabs -->
        <nav class="tab-navigation">
            <button class="tab-btn active" data-tab="dashboard">🏠 Reef Dashboard</button>
            <button class="tab-btn" data-tab="users">👥 School of Users</button>
            <button class="tab-btn" data-tab="analytics">📊 Ocean Analytics</button>
            <button class="tab-btn" data-tab="settings">⚙️ Coral Settings</button>
        </nav>

        <!-- Tab Content -->
        <main class="tab-content">
            <!-- Dashboard Tab -->
            <div class="tab-panel active" id="dashboard">
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon">🐠</div>
                        <div class="stat-info">
                            <h3>Active Fish</h3>
                            <p class="stat-number" id="activeFish">1,247</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">🌊</div>
                        <div class="stat-info">
                            <h3>Ocean Depth</h3>
                            <p class="stat-number">2,847m</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">🐚</div>
                        <div class="stat-info">
                            <h3>Shells Collected</h3>
                            <p class="stat-number" id="shellsCollected">892</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">🦈</div>
                        <div class="stat-info">
                            <h3>Shark Alerts</h3>
                            <p class="stat-number">3</p>
                        </div>
                    </div>
                </div>

                <div class="activity-feed">
                    <h3>🌊 Recent Ocean Activity</h3>
                    <div class="activity-item">
                        <span class="activity-time">2 minutes ago</span>
                        <span class="activity-text">🐠 Nemo joined the anemone group</span>
                    </div>
                    <div class="activity-item">
                        <span class="activity-time">5 minutes ago</span>
                        <span class="activity-text">🐟 Dory found her way back to the reef</span>
                    </div>
                    <div class="activity-item">
                        <span class="activity-time">12 minutes ago</span>
                        <span class="activity-text">🦈 Bruce reported "Fish are friends, not food"</span>
                    </div>
                </div>
            </div>

            <!-- Users Tab -->
            <div class="tab-panel" id="users">
                <div class="users-header">
                    <h3>🐠 School of Users</h3>
                    <button class="add-user-btn">+ Add New Fish</button>
                </div>
                <div class="users-grid">
                    <div class="user-card">
                        <div class="user-avatar">🐠</div>
                        <div class="user-info">
                            <h4>Nemo</h4>
                            <p>Clownfish • Active</p>
                        </div>
                        <div class="user-actions">
                            <button class="btn-edit">✏️</button>
                            <button class="btn-delete">🗑️</button>
                        </div>
                    </div>
                    <div class="user-card">
                        <div class="user-avatar">🐟</div>
                        <div class="user-info">
                            <h4>Dory</h4>
                            <p>Blue Tang • Swimming</p>
                        </div>
                        <div class="user-actions">
                            <button class="btn-edit">✏️</button>
                            <button class="btn-delete">🗑️</button>
                        </div>
                    </div>
                    <div class="user-card">
                        <div class="user-avatar">🦈</div>
                        <div class="user-info">
                            <h4>Bruce</h4>
                            <p>Great White • Vegetarian</p>
                        </div>
                        <div class="user-actions">
                            <button class="btn-edit">✏️</button>
                            <button class="btn-delete">🗑️</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Analytics Tab -->
            <div class="tab-panel" id="analytics">
                <h3>📊 Ocean Analytics</h3>
                <div class="chart-container">
                    <div class="chart-placeholder">
                        <p>🐠 Fish Population Over Time</p>
                        <div class="mock-chart"></div>
                    </div>
                </div>
            </div>

            <!-- Settings Tab -->
            <div class="tab-panel" id="settings">
                <h3>⚙️ Coral Settings</h3>
                <div class="settings-form">
                    <div class="setting-item">
                        <label>Ocean Temperature</label>
                        <input type="range" min="20" max="30" value="24">
                        <span>24°C</span>
                    </div>
                    <div class="setting-item">
                        <label>Current Strength</label>
                        <select>
                            <option>Gentle</option>
                            <option>Moderate</option>
                            <option>Strong</option>
                        </select>
                    </div>
                    <div class="setting-item">
                        <label>Bubble Intensity</label>
                        <input type="range" min="1" max="10" value="5">
                        <span>5/10</span>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Floating Action Button -->
    <button class="fab" id="fabBtn">
        <span class="fab-icon">🐠</span>
        <span class="fab-text">Need Help? Ask Dory!</span>
    </button>

    <!-- Modal for Help -->
    <div class="modal" id="helpModal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2>🐟 Dory's Help Center</h2>
            <p>"Just keep swimming! Here are some tips for navigating the ocean console:"</p>
            <ul>
                <li>🏠 Use the Reef Dashboard to see your ocean overview</li>
                <li>👥 Manage your school of users in the Users tab</li>
                <li>📊 Check ocean analytics for insights</li>
                <li>⚙️ Adjust coral settings to your liking</li>
            </ul>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
